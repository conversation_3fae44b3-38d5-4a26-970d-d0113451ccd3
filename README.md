# Splill - 精确分账应用

Splill 是一个专为多人共同消费场景设计的精确分账工具，特别适用于旅行、合租等按时间参与度不同的分账需求。

## 功能特点

- **按时间精确分账**: 基于每个人在不同时间段的参与状态，自动计算合理的分账金额
- **参与度可视化**: 直观的日历界面，方便设置每个参与者的参与状态
- **结果分享导出**: 支持导出分账结果为图片，方便分享给他人
- **本地数据存储**: 使用浏览器存储保存账单数据，无需注册登录

## 技术栈

- **前端框架**: React 19 + TypeScript
- **UI库**: Ant Design V5
- **路由**: React Router DOM 7
- **构建工具**: Vite 7
- **CSS方案**: CSS-in-JS (Ant Design内置)

## 项目结构

```
src/
├── components/           # 可复用组件
│   ├── BillForm/        # 账单创建表单
│   ├── ParticipationCalendar/ # 参与度设置日历
│   └── ResultTable/     # 分账结果表格
├── pages/               # 页面组件
│   ├── Home/           # 首页
│   ├── CreateBill/     # 创建账单页面
│   ├── EditBill/       # 编辑账单页面
│   └── BillResult/     # 分账结果页面
├── contexts/            # React上下文
│   └── BillContext.tsx # 账单数据上下文
├── hooks/               # 自定义Hook
│   └── useBillCalculator.ts # 分账计算逻辑
├── utils/              # 工具函数
│   ├── storage.ts      # 本地存储相关
│   └── calculator.ts   # 计算相关函数
├── types/              # 类型定义
└── App.tsx             # 根组件
```

## 快速开始

1. 安装依赖

```bash
npm install
```

2. 启动开发服务器

```bash
npm run dev
```

3. 打开浏览器，访问 http://localhost:5173/

## 使用场景示例

假设您和朋友正在旅行，有一个7天的租车订单：
- 前2天全部5人使用
- 中间3天只有3人使用
- 最后2天只有2人使用

使用Splill，您可以：
1. 创建一个新账单，设置总金额和天数
2. 添加所有参与者
3. 在日历界面上设置每个人每天的参与状态
4. 系统自动计算每个人应该支付的金额
5. 分享结果给所有参与者

## 待开发功能

- [ ] 账单数据导出/导入功能
- [ ] 多币种支持
- [ ] 分账模式选择(按天、按人次等)
- [ ] 云同步功能
- [ ] 移动端适配优化

## 许可

MIT