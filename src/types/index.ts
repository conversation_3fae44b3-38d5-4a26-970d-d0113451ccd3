// 账单类型定义
export interface Bill {
  id: string;
  name: string;
  totalAmount: number;
  days: number;
  startDate: string; // ISO日期字符串
  participants: Participant[];
  createdAt: string; // ISO日期字符串
  updatedAt: string; // ISO日期字符串
  // 组合订单相关字段
  isCombo?: boolean; // 是否为组合订单
  subBills?: SubBill[]; // 子订单列表
  parentId?: string; // 父订单ID（如果是子订单）
}

// 参与者类型定义
export interface Participant {
  id: string;
  name: string;
  participationDays: boolean[]; // 每天的参与状态
  amountToPay: number; // 计算结果
}

// 子订单类型定义
export interface SubBill {
  id: string;
  name: string;
  amount: number;
  participants: { id: string; name: string; amountToPay: number }[];
}

// 新建账单表单类型
export interface BillFormData {
  name: string;
  totalAmount: number;
  days: number;
  startDate: string;
  participants: { name: string }[];
  isCombo?: boolean;
} 

// 组合订单表单类型
export interface ComboBillFormData {
  name: string;
  subBills: {
    name: string;
    amount: number;
    participants: string[]; // 参与者ID列表
  }[];
  participants: { id: string; name: string }[];
} 