import React from 'react';
import { Typography, Breadcrumb } from 'antd';
import { Link, useNavigate } from 'react-router-dom';
import BillForm from '../../components/BillForm';
import type { Bill } from '../../types';
import { HomeOutlined, FileAddOutlined } from '@ant-design/icons';

const { Title } = Typography;

const CreateBillPage: React.FC = () => {
  const navigate = useNavigate();

  const handleCreateSuccess = (bill: Bill) => {
    navigate(`/bill/${bill.id}`);
  };

  return (
    <div style={{ padding: 24 }}>
      <Breadcrumb 
        items={[
          {
            title: (
              <Link to="/">
                <HomeOutlined /> 首页
              </Link>
            ),
          },
          {
            title: (
              <>
                <FileAddOutlined /> 创建账单
              </>
            ),
          },
        ]}
        style={{ marginBottom: 16 }}
      />
      
      <Title level={2}>创建新账单</Title>
      <BillForm onSuccess={handleCreateSuccess} />
    </div>
  );
};

export default CreateBillPage;
