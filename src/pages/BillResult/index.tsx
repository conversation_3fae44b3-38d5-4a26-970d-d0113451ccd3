import React, { useState, useEffect } from 'react';
import { Typo<PERSON>, <PERSON><PERSON>crumb, Button, Spin, Result } from 'antd';
import { Link, useParams, useNavigate } from 'react-router-dom';
import ResultTable from '../../components/ResultTable';
import type { Bill } from '../../types';
import { useBill } from '../../contexts/BillContext';
import { HomeOutlined, FileTextOutlined, EditOutlined } from '@ant-design/icons';

const { Title } = Typography;

const BillResultPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { getBill, loading } = useBill();
  const [bill, setBill] = useState<Bill | undefined>(undefined);
  const navigate = useNavigate();

  // 加载账单数据
  useEffect(() => {
    if (id) {
      const foundBill = getBill(id);
      setBill(foundBill);
    }
  }, [id, getBill]);

  // 加载中
  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  // 账单不存在
  if (!bill && !loading) {
    return (
      <Result
        status="404"
        title="账单不存在"
        subTitle="您要查找的账单不存在或已被删除"
        extra={
          <Button type="primary" onClick={() => navigate('/')}>
            返回首页
          </Button>
        }
      />
    );
  }

  return (
    <div style={{ padding: 24 }}>
      <Breadcrumb 
        items={[
          {
            title: (
              <Link to="/">
                <HomeOutlined /> 首页
              </Link>
            ),
          },
          {
            title: (
              <>
                <FileTextOutlined /> 账单详情
              </>
            ),
          },
        ]}
        style={{ marginBottom: 16 }}
      />
      
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>{bill?.name}</Title>
        <Button
          type="primary"
          icon={<EditOutlined />}
          onClick={() => navigate(`/bill/edit/${id}`)}
        >
          编辑账单
        </Button>
      </div>

      {bill && <ResultTable bill={bill} />}
    </div>
  );
};

export default BillResultPage; 