import React, { useState, useEffect } from 'react';
import { Typo<PERSON>, Breadcrumb, <PERSON><PERSON>, Spin, Result, Button } from 'antd';
import { Link, useParams, useNavigate } from 'react-router-dom';
import BillForm from '../../components/BillForm';
import ParticipationCalendar from '../../components/ParticipationCalendar';
import type { Bill } from '../../types';
import { useBill } from '../../contexts/BillContext';
import { HomeOutlined, EditOutlined, CalendarOutlined, FormOutlined } from '@ant-design/icons';

const { Title } = Typography;

type TabKey = 'basic' | 'participation';

const EditBillPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { getBill, loading } = useBill();
  const [bill, setBill] = useState<Bill | undefined>(undefined);
  const [activeTab, setActiveTab] = useState<TabKey>('basic');
  const navigate = useNavigate();

  // 加载账单数据
  useEffect(() => {
    if (id) {
      const foundBill = getBill(id);
      setBill(foundBill);
    }
  }, [id, getBill]);

  const handleEditSuccess = (updatedBill: Bill) => {
    setBill(updatedBill);
    setActiveTab('participation');
  };

  const handleSaveParticipation = () => {
    // 返回账单详情页面
    if (id) {
      navigate(`/bill/${id}`);
    }
  };

  // 加载中
  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  // 账单不存在
  if (!bill && !loading) {
    return (
      <Result
        status="404"
        title="账单不存在"
        subTitle="您要查找的账单不存在或已被删除"
        extra={
          <Button type="primary" onClick={() => navigate('/')}>
            返回首页
          </Button>
        }
      />
    );
  }

  return (
    <div style={{ padding: 24 }}>
      <Breadcrumb 
        items={[
          {
            title: (
              <Link to="/">
                <HomeOutlined /> 首页
              </Link>
            ),
          },
          {
            title: (
              <>
                <EditOutlined /> 编辑账单
              </>
            ),
          },
        ]}
        style={{ marginBottom: 16 }}
      />
      
      <Title level={2}>编辑账单: {bill?.name}</Title>

      <Tabs
        activeKey={activeTab}
        onChange={(key) => setActiveTab(key as TabKey)}
        items={[
          {
            key: 'basic',
            label: (
              <span>
                <FormOutlined /> 基本信息
              </span>
            ),
            children: bill && <BillForm initialValues={bill} onSuccess={handleEditSuccess} />,
          },
          {
            key: 'participation',
            label: (
              <span>
                <CalendarOutlined /> 参与情况
              </span>
            ),
            children: bill && <ParticipationCalendar bill={bill} onSave={handleSaveParticipation} />,
          },
        ]}
      />
    </div>
  );
};

export default EditBillPage; 