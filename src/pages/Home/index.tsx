import React from 'react';
import { List, Card, Button, Typography, Popconfirm, Empty, Spin } from 'antd';
import { PlusOutlined, EditOutlined, DeleteOutlined, ArrowRightOutlined } from '@ant-design/icons';
import { Link, useNavigate } from 'react-router-dom';
import { useBill } from '../../contexts/BillContext';
import dayjs from 'dayjs';

const { Title, Text } = Typography;

const HomePage: React.FC = () => {
  const { bills, removeBill, loading } = useBill();
  const navigate = useNavigate();

  if (loading) {
    return (
      <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <Spin size="large" tip="加载中..." />
      </div>
    );
  }

  return (
    <div style={{ padding: 24 }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <Title level={2}>我的账单</Title>
        <Button
          type="primary"
          icon={<PlusOutlined />}
          onClick={() => navigate('/bill/create')}
        >
          创建新账单
        </Button>
      </div>

      {bills.length === 0 ? (
        <Card>
          <Empty
            description="暂无账单"
            image={Empty.PRESENTED_IMAGE_SIMPLE}
          >
            <Button 
              type="primary" 
              onClick={() => navigate('/bill/create')}
              icon={<PlusOutlined />}
            >
              创建第一个账单
            </Button>
          </Empty>
        </Card>
      ) : (
        <List
          grid={{
            gutter: 16,
            xs: 1,
            sm: 1,
            md: 2,
            lg: 3,
            xl: 4,
            xxl: 4,
          }}
          dataSource={bills}
          renderItem={(bill) => (
            <List.Item>
              <Card
                title={bill.name}
                hoverable
                actions={[
                  <Link to={`/bill/edit/${bill.id}`} key="edit">
                    <EditOutlined /> 编辑
                  </Link>,
                  <Popconfirm
                    key="delete"
                    title="删除账单"
                    description="确定要删除这个账单吗？此操作不可恢复。"
                    onConfirm={() => removeBill(bill.id)}
                    okText="确定"
                    cancelText="取消"
                  >
                    <Button type="text" danger icon={<DeleteOutlined />}>
                      删除
                    </Button>
                  </Popconfirm>,
                  <Link to={`/bill/${bill.id}`} key="view">
                    <ArrowRightOutlined /> 查看结果
                  </Link>,
                ]}
              >
                <div style={{ marginBottom: 8 }}>
                  <Text strong>总金额：</Text>
                  <Text>¥{bill.totalAmount.toFixed(2)}</Text>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>参与人数：</Text>
                  <Text>{bill.participants.length}人</Text>
                </div>
                <div style={{ marginBottom: 8 }}>
                  <Text strong>时间跨度：</Text>
                  <Text>{bill.days}天</Text>
                </div>
                <div>
                  <Text strong>创建日期：</Text>
                  <Text>{dayjs(bill.createdAt).format('YYYY-MM-DD')}</Text>
                </div>
              </Card>
            </List.Item>
          )}
        />
      )}
    </div>
  );
};

export default HomePage;
