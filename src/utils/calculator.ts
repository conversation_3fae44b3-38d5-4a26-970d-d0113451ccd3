import type { Bill } from "../types";

/**
 * 计算每个参与者应付的金额
 * @param bill 账单数据
 * @returns 更新后的账单数据，包含每个参与者的应付金额
 */
export const calculateBill = (bill: Bill): Bill => {
  // 如果是组合订单，使用组合订单计算逻辑
  if (bill.isCombo && bill.subBills && bill.subBills.length > 0) {
    return calculateComboBill(bill);
  }
  
  const { totalAmount, participants, days } = bill;
  
  // 计算每天的总参与人数
  const dailyParticipantCounts: number[] = Array(days).fill(0);
  
  // 统计每天的参与人数
  participants.forEach(participant => {
    participant.participationDays.forEach((isParticipating, dayIndex) => {
      if (isParticipating) {
        dailyParticipantCounts[dayIndex]++;
      }
    });
  });
  
  // 检查是否有一天没有任何人参与
  const hasZeroParticipationDay = dailyParticipantCounts.some(count => count === 0);
  if (hasZeroParticipationDay) {
    // 找出哪些天没有人参与
    const zeroDays = dailyParticipantCounts.map((count, index) => count === 0 ? index : -1).filter(day => day !== -1);
    console.warn(`警告: 第${zeroDays.map(day => day + 1).join(', ')}天没有任何参与者，这可能导致计算不准确`);
  }
  
  // 计算每天的单位成本
  const dailyCost = totalAmount / days;
  
  // 计算每个参与者应付的总金额（未四舍五入）
  const initialParticipants = participants.map(participant => {
    let amountToPay = 0;
    
    participant.participationDays.forEach((isParticipating, dayIndex) => {
      if (isParticipating && dailyParticipantCounts[dayIndex] > 0) {
        // 每天的费用按参与人数平均分配
        amountToPay += dailyCost / dailyParticipantCounts[dayIndex];
      }
    });
    
    return {
      ...participant,
      amountToPay // 先保留精确值，最后一起处理舍入问题
    };
  });
  
  // 对每个参与者的金额进行四舍五入，保留两位小数
  const roundedParticipants = initialParticipants.map((participant, index) => {
    // 为最后一个参与者调整金额，使总和精确等于totalAmount
    let finalAmount;
    if (index === initialParticipants.length - 1) {
      // 计算前面所有参与者舍入后的总和
      const previousSum = initialParticipants.slice(0, index).reduce(
        (sum, p) => sum + Math.round(p.amountToPay * 100) / 100, 
        0
      );
      // 最后一个参与者的金额 = 总金额 - 前面所有参与者的金额
      finalAmount = Math.round((totalAmount - previousSum) * 100) / 100;
    } else {
      // 其他参与者正常舍入
      finalAmount = Math.round(participant.amountToPay * 100) / 100;
    }
    
    return {
      ...participant,
      amountToPay: finalAmount
    };
  });
  
  // 再次验证总和
  const finalTotal = roundedParticipants.reduce((sum, p) => sum + p.amountToPay, 0);
  
  // 如果总和与账单金额有差异，调整第一个参与者的金额
  if (Math.abs(finalTotal - totalAmount) > 0.01) {
    const diff = Math.round((totalAmount - finalTotal) * 100) / 100;
    if (roundedParticipants.length > 0) {
      roundedParticipants[0].amountToPay += diff;
      console.warn(`调整了${diff}元差额到第一个参与者`);
    }
  }
  
  return {
    ...bill,
    participants: roundedParticipants
  };
};

/**
 * 计算组合订单中每个参与者应付的金额
 * @param bill 组合订单数据
 * @returns 更新后的组合订单数据，包含每个参与者的应付金额
 */
export const calculateComboBill = (bill: Bill): Bill => {
  if (!bill.subBills || !bill.isCombo) {
    return bill;
  }

  // 创建一个参与者ID到金额的映射
  const participantTotalMap: Record<string, number> = {};

  // 初始化所有参与者的应付金额为0
  bill.participants.forEach(p => {
    participantTotalMap[p.id] = 0;
  });

  // 计算每个子订单的分账，并累加到总金额
  bill.subBills.forEach(subBill => {
    // 子订单参与人数
    const participantsCount = subBill.participants.length;
    if (participantsCount === 0) return;

    // 计算每人均分的金额
    const amountPerPerson = subBill.amount / participantsCount;

    // 为每个参与者分配金额
    subBill.participants.forEach((subParticipant, index) => {
      // 最后一个参与者处理舍入误差
      if (index === participantsCount - 1) {
        const previousSum = subBill.participants.slice(0, index)
          .reduce((sum, p) => sum + Math.round(amountPerPerson * 100) / 100, 0);
        const lastAmount = Math.round((subBill.amount - previousSum) * 100) / 100;
        subBill.participants[index].amountToPay = lastAmount;
        participantTotalMap[subParticipant.id] = (participantTotalMap[subParticipant.id] || 0) + lastAmount;
      } else {
        const roundedAmount = Math.round(amountPerPerson * 100) / 100;
        subBill.participants[index].amountToPay = roundedAmount;
        participantTotalMap[subParticipant.id] = (participantTotalMap[subParticipant.id] || 0) + roundedAmount;
      }
    });
  });

  // 更新每个参与者的总应付金额
  const updatedParticipants = bill.participants.map(p => ({
    ...p,
    amountToPay: participantTotalMap[p.id] || 0
  }));

  // 验证总金额
  const calculatedTotal = updatedParticipants.reduce((sum, p) => sum + p.amountToPay, 0);
  const billTotal = bill.subBills.reduce((sum, sb) => sum + sb.amount, 0);

  // 处理可能的舍入误差
  if (Math.abs(calculatedTotal - billTotal) > 0.01 && updatedParticipants.length > 0) {
    const diff = Math.round((billTotal - calculatedTotal) * 100) / 100;
    updatedParticipants[0].amountToPay += diff;
    console.warn(`调整了${diff}元差额到第一个参与者`);
  }

  return {
    ...bill,
    totalAmount: billTotal, // 确保总金额等于所有子订单的和
    participants: updatedParticipants,
    subBills: bill.subBills
  };
}; 