import type { Bill } from '../types';

const STORAGE_KEY = 'splill_bills';

/**
 * 保存账单列表到本地存储
 * @param bills 账单列表
 */
export const saveBills = (bills: Bill[]): void => {
  localStorage.setItem(STORAGE_KEY, JSON.stringify(bills));
};

/**
 * 从本地存储获取账单列表
 * @returns 账单列表，如果不存在则返回空数组
 */
export const getBills = (): Bill[] => {
  const billsJson = localStorage.getItem(STORAGE_KEY);
  return billsJson ? JSON.parse(billsJson) : [];
};

/**
 * 保存单个账单到本地存储
 * @param bill 账单对象
 */
export const saveBill = (bill: Bill): void => {
  const bills = getBills();
  const index = bills.findIndex(b => b.id === bill.id);
  
  if (index !== -1) {
    // 更新已有账单
    bills[index] = bill;
  } else {
    // 添加新账单
    bills.push(bill);
  }
  
  saveBills(bills);
};

/**
 * 根据ID获取账单
 * @param id 账单ID
 * @returns 账单对象，如果不存在则返回undefined
 */
export const getBillById = (id: string): Bill | undefined => {
  const bills = getBills();
  return bills.find(bill => bill.id === id);
};

/**
 * 删除账单
 * @param id 账单ID
 */
export const deleteBill = (id: string): void => {
  const bills = getBills();
  const filteredBills = bills.filter(bill => bill.id !== id);
  saveBills(filteredBills);
}; 