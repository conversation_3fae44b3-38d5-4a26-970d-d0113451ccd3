import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Button } from '../ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';
import { Share, Download, FileSpreadsheet, Calendar, Edit, BarChart3, Grid, Table as TableIcon } from 'lucide-react';
import { toast } from 'sonner';
import type { Bill, Participant } from '../../types';
import dayjs from 'dayjs';
import html2canvas from 'html2canvas';
import { useBill } from '../../contexts/BillContext';
import ComboResultTable from '../ComboResultTable';
import ParticipationHeatmap from '../Visualization/ParticipationHeatmap';
import AmountBarChart from '../Visualization/AmountBarChart';
import ComboFlowChart from '../Visualization/ComboFlowChart';

interface ResultTableProps {
  bill: Bill;
  onBack?: () => void;
  onEditBasicInfo?: () => void;
}

const ResultTable: React.FC<ResultTableProps> = ({ bill, onBack, onEditBasicInfo }) => {
  const [showVisualization, setShowVisualization] = useState(false);
  const { getBill } = useBill();

  // 确保使用最新的账单数据
  const latestBill = getBill(bill.id) || bill;

  // 如果是组合账单，使用ComboResultTable
  if (latestBill.isCombo) {
    return <ComboResultTable bill={latestBill} onEditBasicInfo={onEditBasicInfo} />;
  }

  // 导出功能
  const handleExport = async () => {
    try {
      const element = document.getElementById('result-table-content');
      if (element) {
        const canvas = await html2canvas(element);
        const link = document.createElement('a');
        link.download = `${latestBill.name}-分账结果.png`;
        link.href = canvas.toDataURL();
        link.click();
        toast.success('导出成功');
      }
    } catch (error) {
      toast.error('导出失败');
    }
  };

  return (
    <div id="result-table-content">
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-xl">{latestBill.name} - 分账结果</CardTitle>
              <p className="text-sm text-muted-foreground mt-1">
                总金额: ¥{latestBill.totalAmount.toFixed(2)} |
                时间: {dayjs(latestBill.startDate).format('YYYY-MM-DD')} 至 {dayjs(latestBill.startDate).add(latestBill.days - 1, 'day').format('YYYY-MM-DD')} ({latestBill.days}天)
              </p>
            </div>
            <div className="flex gap-2">
              {onBack && (
                <Button variant="outline" onClick={onBack}>
                  <Edit className="h-4 w-4 mr-2" />
                  返回编辑
                </Button>
              )}
              {onEditBasicInfo && (
                <Button variant="outline" onClick={onEditBasicInfo}>
                  <Edit className="h-4 w-4 mr-2" />
                  编辑基本信息
                </Button>
              )}
              <Button variant="outline" onClick={handleExport}>
                <Download className="h-4 w-4 mr-2" />
                导出图片
              </Button>
              <Button variant="outline" onClick={() => setShowVisualization(!showVisualization)}>
                <BarChart3 className="h-4 w-4 mr-2" />
                {showVisualization ? '隐藏' : '显示'}图表
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>参与者</TableHead>
                <TableHead className="text-center">参与天数</TableHead>
                <TableHead className="text-center">参与比例</TableHead>
                <TableHead className="text-right">应付金额</TableHead>
                <TableHead className="text-right">日均金额</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {latestBill.participants.map((participant) => {
                const daysCount = participant.participationDays.filter(Boolean).length;
                const percentage = ((daysCount / latestBill.days) * 100).toFixed(1);
                const avgPerDay = daysCount > 0 ? participant.amountToPay / daysCount : 0;

                return (
                  <TableRow key={participant.id}>
                    <TableCell className="font-medium">{participant.name}</TableCell>
                    <TableCell className="text-center">{daysCount} / {latestBill.days}</TableCell>
                    <TableCell className="text-center">{percentage}%</TableCell>
                    <TableCell className="text-right font-medium">¥{participant.amountToPay.toFixed(2)}</TableCell>
                    <TableCell className="text-right">¥{avgPerDay.toFixed(2)}</TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>

          {showVisualization && (
            <div className="mt-6 space-y-6">
              <AmountBarChart bill={latestBill} />
              <ParticipationHeatmap bill={latestBill} />
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};

export default ResultTable;