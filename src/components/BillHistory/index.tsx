import React, { useState } from 'react';
import { But<PERSON> } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '../ui/dropdown-menu';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '../ui/dialog';
import {
  FileText,
  Trash2,
  Copy,
  Edit,
  Calendar,
  MoreHorizontal,
  Plus,
  Layers
} from 'lucide-react';
import { cn } from '../../lib/utils';
import { format } from 'date-fns';
import type { Bill } from '../../types';

interface BillHistoryProps {
  bills: Bill[];
  currentBill: Bill | null;
  onSelect: (id: string) => void;
  onCreateNew: () => void;
  onCreateNewCombo?: () => void;
  onDuplicate: (id: string) => void;
  onEditBasicInfo: (id: string) => void;
  onEditParticipation: (id: string) => void;
  onDelete: (id: string) => void;
  width: number;
  onWidthChange: (width: number) => void;
}

const BillHistory: React.FC<BillHistoryProps> = ({
  bills,
  currentBill,
  onSelect,
  onCreateNew,
  onCreateNewCombo,
  onDuplicate,
  onEditBasicInfo,
  onEditParticipation,
  onDelete,
  width,
  onWidthChange
}) => {
  const [resizing, setResizing] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [billToDelete, setBillToDelete] = useState<string | null>(null);

  // 显示删除确认对话框
  const showDeleteConfirm = (e: React.MouseEvent, id: string) => {
    e.stopPropagation();
    setBillToDelete(id);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (billToDelete) {
      onDelete(billToDelete);
      setBillToDelete(null);
    }
    setDeleteDialogOpen(false);
  };

  // 处理拖动调整宽度
  const handleResizeStart = (e: React.MouseEvent) => {
    e.preventDefault();
    setResizing(true);
    
    const startX = e.clientX;
    const startWidth = width;
    
    const handleMouseMove = (moveEvent: MouseEvent) => {
      const newWidth = Math.max(250, Math.min(500, startWidth + moveEvent.clientX - startX));
      onWidthChange(newWidth);
    };
    
    const handleMouseUp = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
      setResizing(false);
    };
    
    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const sortedBills = bills.sort((a, b) => new Date(b.updatedAt).getTime() - new Date(a.updatedAt).getTime());

  return (
    <div className="h-full flex flex-col bg-card" style={{ width: `${width}px` }}>
      <div className="flex items-center justify-between p-4 border-b">
        <h3 className="text-lg font-semibold">账单历史</h3>
        <div className="flex items-center gap-2">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button size="sm">
                <Plus className="h-4 w-4 mr-2" />
                创建账单
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={onCreateNew}>
                <Plus className="h-4 w-4 mr-2" />
                创建普通账单
              </DropdownMenuItem>
              {onCreateNewCombo && (
                <DropdownMenuItem onClick={onCreateNewCombo}>
                  <Layers className="h-4 w-4 mr-2" />
                  创建组合账单
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
          <div
            className={cn(
              "w-1 h-6 bg-border hover:bg-primary/50 cursor-ew-resize",
              resizing && "bg-primary"
            )}
            onMouseDown={handleResizeStart}
          />
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-2">
        {bills.length === 0 ? (
          <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
            <FileText className="h-8 w-8 mb-2" />
            <p className="text-sm">暂无历史账单</p>
          </div>
        ) : (
          <div className="space-y-2">
            {sortedBills.map((bill) => (
              <Card
                key={bill.id}
                className={cn(
                  "cursor-pointer transition-colors hover:bg-accent",
                  currentBill?.id === bill.id && "bg-accent border-primary"
                )}
                onClick={() => onSelect(bill.id)}
              >
                <CardContent className="p-3">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3 flex-1">
                      <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center flex-shrink-0">
                        <FileText className="h-4 w-4 text-primary" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <h4 className="text-sm font-medium truncate">{bill.name}</h4>
                          {bill.isCombo && (
                            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-blue-100 text-blue-800">
                              组合
                            </span>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground">
                          ¥{bill.totalAmount.toFixed(2)} · {format(new Date(bill.createdAt), 'yyyy-MM-dd')}
                        </p>
                      </div>
                    </div>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-8 w-8 p-0"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          onDuplicate(bill.id);
                        }}>
                          <Copy className="h-4 w-4 mr-2" />
                          复制账单
                        </DropdownMenuItem>
                        <DropdownMenuItem onClick={(e) => {
                          e.stopPropagation();
                          onEditBasicInfo(bill.id);
                        }}>
                          <Edit className="h-4 w-4 mr-2" />
                          编辑基本信息
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          disabled={bill.isCombo}
                          onClick={(e) => {
                            e.stopPropagation();
                            onEditParticipation(bill.id);
                          }}
                        >
                          <Calendar className="h-4 w-4 mr-2" />
                          编辑参与度
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          className="text-destructive"
                          onClick={(e) => {
                            e.stopPropagation();
                            showDeleteConfirm(e as any, bill.id);
                          }}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          删除账单
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </div>

      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>确定要删除这个账单吗?</DialogTitle>
            <DialogDescription>
              删除后将无法恢复，所有相关数据都将丢失。
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)}>
              取消
            </Button>
            <Button variant="destructive" onClick={handleDeleteConfirm}>
              确定删除
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default BillHistory; 