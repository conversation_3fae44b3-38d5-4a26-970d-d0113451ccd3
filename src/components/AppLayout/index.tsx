import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '../ui/button';
import { Sheet, <PERSON><PERSON><PERSON>onte<PERSON>, She<PERSON><PERSON>eader, Sheet<PERSON>itle, SheetTrigger } from '../ui/sheet';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Steps } from '../ui/steps';
import { Menu, ChevronLeft, ChevronRight } from 'lucide-react';
import { cn } from '../../lib/utils';
import BillHistory from '../BillHistory';
import BillForm from '../BillForm';
import ComboBillForm from '../ComboBillForm';
import ParticipationCalendar from '../ParticipationCalendar';
import ResultTable from '../ResultTable';
import { useBill } from '../../contexts/BillContext';
import type { Bill } from '../../types';
import { v4 as uuidv4 } from 'uuid';

interface AppLayoutProps {
  children?: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = () => {
  const [currentStep, setCurrentStep] = useState(0);
  const [bill, setBill] = useState<Bill | null>(null);
  const [collapsed, setCollapsed] = useState(false);
  const [mobileDrawerVisible, setMobileDrawerVisible] = useState(false);
  const [isMobile, setIsMobile] = useState(window.innerWidth < 768);
  const [isEditingBasicInfo, setIsEditingBasicInfo] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState(280);
  const [billType, setBillType] = useState<'normal' | 'combo'>('normal');
  const { bills, getBill, removeBill, createBill } = useBill();

  // 检测屏幕大小变化
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // 同步当前账单与bills中的最新数据
  useEffect(() => {
    if (bill && bills.length > 0) {
      // 查找当前bill在bills数组中的最新版本
      const updatedBill = bills.find(b => b.id === bill.id);
      if (updatedBill && JSON.stringify(updatedBill) !== JSON.stringify(bill)) {
        // 如果找到了更新的版本，则更新当前bill
        setBill(updatedBill);
      }
    }
  }, [bill, bills]);

  // 更新CSS变量来控制侧边栏宽度
  useEffect(() => {
    document.documentElement.style.setProperty('--sidebar-width', `${sidebarWidth}px`);
  }, [sidebarWidth]);

  // 处理创建账单完成
  const handleBillCreated = (newBill: Bill) => {
    setBill(newBill);
    if (isEditingBasicInfo) {
      setIsEditingBasicInfo(false);
      setCurrentStep(2); // 回到结果页面
    } else {
      if (newBill.isCombo) {
        // 组合账单直接跳到结果页面
        setCurrentStep(2);
      } else {
        // 普通账单前进到参与度设置步骤
        setCurrentStep(1);
      }
    }
  };

  // 处理参与度设置完成
  const handleParticipationSaved = () => {
    // 获取最新的账单数据
    if (bill) {
      const latestBill = getBill(bill.id);
      if (latestBill) {
        setBill(latestBill);
      }
    }
    setCurrentStep(2); // 前进到结果页面
  };

  // 返回编辑
  const handleBackToEdit = () => {
    setCurrentStep(1);
  };

  // 查看历史账单
  const handleHistoryItemClick = (id: string) => {
    const historicalBill = getBill(id);
    if (historicalBill) {
      setBill(historicalBill);
      setCurrentStep(2); // 直接跳转到结果页面
      if (isMobile) {
        setMobileDrawerVisible(false);
      }
    }
  };

  // 创建新账单
  const handleCreateNew = () => {
    setBill(null);
    setBillType('normal');
    setCurrentStep(0);
    if (isMobile) {
      setMobileDrawerVisible(false);
    }
  };

  // 创建新组合账单
  const handleCreateNewCombo = () => {
    setBill(null);
    setBillType('combo');
    setCurrentStep(0);
    if (isMobile) {
      setMobileDrawerVisible(false);
    }
  };

  // 复制账单
  const handleDuplicateBill = (id: string) => {
    const sourceBill = getBill(id);
    if (!sourceBill) {
      return;
    }
    
    try {
      // 创建新账单，复制原账单的大部分信息，但生成新的ID
      const newBillData = {
        name: `${sourceBill.name} (副本)`,
        totalAmount: sourceBill.totalAmount,
        days: sourceBill.days,
        startDate: sourceBill.startDate,
        participants: sourceBill.participants.map(p => ({
          id: uuidv4(), // 生成新ID
          name: p.name,
          participationDays: [...p.participationDays], // 复制参与状态
          amountToPay: 0 // 金额会在计算时填充
        }))
      };
      
      // 创建新账单
      const newBill = createBill(newBillData);
      
      // 设置当前账单为新创建的账单
      setBill(newBill);
      setCurrentStep(2); // 直接跳转到结果页面
    } catch (error) {
      console.error('复制账单失败:', error);
    }
  };

  // 编辑账单基本信息
  const handleEditBasicInfo = (id: string) => {
    const billToEdit = getBill(id);
    if (billToEdit) {
      setBill(billToEdit);
      setIsEditingBasicInfo(true); // 标记为编辑基本信息模式
      setCurrentStep(0); // 跳转到表单页面
      if (isMobile) {
        setMobileDrawerVisible(false);
      }
    }
  };

  // 编辑历史账单参与度
  const handleEditParticipation = (id: string) => {
    const billToEdit = getBill(id);
    if (billToEdit) {
      setBill(billToEdit);
      setCurrentStep(1); // 跳转到参与度设置步骤
      if (isMobile) {
        setMobileDrawerVisible(false);
      }
    }
  };

  // 切换侧边栏折叠状态
  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  // 渲染当前步骤内容
  const renderStepContent = () => {
    switch (currentStep) {
      case 0: // 创建/编辑账单
        if (isEditingBasicInfo && bill) {
          // 编辑现有账单
          if (bill.isCombo) {
            return <ComboBillForm initialValues={bill} onSuccess={handleBillCreated} />;
          } else {
            return <BillForm initialValues={bill} onSuccess={handleBillCreated} />;
          }
        } else {
          // 创建新账单
          return billType === 'combo' 
            ? <ComboBillForm onSuccess={handleBillCreated} />
            : <BillForm onSuccess={handleBillCreated} />;
        }
      case 1: // 设置参与度
        return bill && !bill.isCombo ? <ParticipationCalendar bill={bill} onSave={handleParticipationSaved} /> : null;
      case 2: // 查看结果
        return bill ? (
          <ResultTable 
            bill={bill} 
            onBack={bill.isCombo ? undefined : handleBackToEdit} 
            onEditBasicInfo={() => {
              setIsEditingBasicInfo(true);
              setCurrentStep(0);
            }} 
          />
        ) : null;
      default:
        return null;
    }
  };

  // 移动端抽屉
  const renderMobileDrawer = () => {
    return (
      <Sheet open={mobileDrawerVisible} onOpenChange={setMobileDrawerVisible}>
        <SheetContent side="left" className="w-80 p-0">
          <SheetHeader className="p-4">
            <SheetTitle>账单历史</SheetTitle>
          </SheetHeader>
          <BillHistory
            bills={bills}
            currentBill={bill}
            onSelect={handleHistoryItemClick}
            onCreateNew={handleCreateNew}
            onCreateNewCombo={handleCreateNewCombo}
            onDuplicate={handleDuplicateBill}
            onEditBasicInfo={handleEditBasicInfo}
            onEditParticipation={handleEditParticipation}
            onDelete={removeBill}
            width={sidebarWidth}
            onWidthChange={setSidebarWidth}
          />
        </SheetContent>
      </Sheet>
    );
  };

  return (
    <div className="min-h-screen bg-background">
      {!isMobile && (
        <>
          <div
            className={cn(
              "fixed left-0 top-0 bottom-0 bg-card border-r transition-all duration-300 z-50",
              collapsed ? "w-0" : `w-[${sidebarWidth}px]`
            )}
            style={{ width: collapsed ? 0 : sidebarWidth }}
          >
            {!collapsed && (
              <BillHistory
                bills={bills}
                currentBill={bill}
                onSelect={handleHistoryItemClick}
                onCreateNew={handleCreateNew}
                onCreateNewCombo={handleCreateNewCombo}
                onDuplicate={handleDuplicateBill}
                onEditBasicInfo={handleEditBasicInfo}
                onEditParticipation={handleEditParticipation}
                onDelete={removeBill}
                width={sidebarWidth}
                onWidthChange={setSidebarWidth}
              />
            )}
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={toggleSidebar}
            className="fixed left-2 top-4 z-50"
          >
            {collapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
          </Button>
        </>
      )}

      <div
        className="flex-1 transition-all duration-300"
        style={{ marginLeft: isMobile ? 0 : (collapsed ? 0 : sidebarWidth) }}
      >
        <main className="p-6 bg-muted/50 min-h-screen">
          <div className="max-w-6xl mx-auto">
            <div className="flex items-center justify-between mb-6">
              <h1 className="text-3xl font-bold text-foreground">Splill 精确分账</h1>
              {isMobile && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setMobileDrawerVisible(true)}
                >
                  <Menu className="h-4 w-4" />
                </Button>
              )}
            </div>

            {currentStep === 0 && !isEditingBasicInfo && (
              <div className="flex gap-4 mb-6">
                <Button
                  variant={billType === 'normal' ? 'default' : 'outline'}
                  onClick={() => setBillType('normal')}
                >
                  创建普通账单
                </Button>
                <Button
                  variant={billType === 'combo' ? 'default' : 'outline'}
                  onClick={() => setBillType('combo')}
                >
                  创建组合账单
                </Button>
              </div>
            )}

            <Steps
              current={currentStep}
              onChange={(current) => {
                // 如果当前在编辑基本信息模式，且切换到其他步骤
                if (isEditingBasicInfo && current !== 0) {
                  setIsEditingBasicInfo(false);
                }
                setCurrentStep(current);
              }}
              items={[
                { title: isEditingBasicInfo ? '编辑基本信息' : (billType === 'combo' ? '创建组合账单' : '创建账单') },
                { title: '设置参与度', disabled: !bill || bill.isCombo },
                { title: '查看结果', disabled: !bill }
              ]}
              className="mb-6"
            />

            <div className="steps-content">
              {renderStepContent()}
            </div>
          </div>
        </main>

        <footer className="bg-card border-t p-4 text-center text-sm text-muted-foreground">
          Splill 分账应用 ©{new Date().getFullYear()} 精确分账，让分账更轻松
        </footer>
      </div>

      {isMobile && renderMobileDrawer()}
    </div>
  );
};

export default AppLayout; 