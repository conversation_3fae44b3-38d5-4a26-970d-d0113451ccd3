import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../ui/table';
import { Button } from '../ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '../ui/dialog';
import type { Bill, SubBill, Participant } from '../../types';

interface ComboResultTableProps {
  bill: Bill;
  onEditBasicInfo?: () => void;
}

const ComboResultTable: React.FC<ComboResultTableProps> = ({ bill, onEditBasicInfo }) => {
  const [selectedSubBill, setSelectedSubBill] = useState<SubBill | null>(null);

  // 确保账单是组合账单
  if (!bill.isCombo || !bill.subBills || bill.subBills.length === 0) {
    return (
      <Card>
        <CardContent>
          <p className="text-red-600">此账单不是有效的组合账单</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>组合账单结果</CardTitle>
          <p className="text-sm text-muted-foreground">
            总金额: ¥{bill.totalAmount.toFixed(2)} | 子订单数: {bill.subBills.length}
          </p>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* 参与者总览 */}
            <div>
              <h4 className="text-lg font-medium mb-4">参与者分摊明细</h4>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>参与者</TableHead>
                    <TableHead className="text-right">应付金额</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {bill.participants.map((participant) => (
                    <TableRow key={participant.id}>
                      <TableCell className="font-medium">{participant.name}</TableCell>
                      <TableCell className="text-right font-medium">
                        ¥{participant.amountToPay.toFixed(2)}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* 子订单明细 */}
            <div>
              <h4 className="text-lg font-medium mb-4">子订单明细</h4>
              <div className="grid gap-4">
                {bill.subBills.map((subBill, index) => (
                  <Card key={subBill.id} className="border">
                    <CardHeader className="pb-3">
                      <div className="flex items-center justify-between">
                        <CardTitle className="text-base">{subBill.name}</CardTitle>
                        <span className="text-lg font-bold">¥{subBill.amount.toFixed(2)}</span>
                      </div>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        <p className="text-sm text-muted-foreground">
                          参与人数: {subBill.participants.length}人
                        </p>
                        <div className="flex flex-wrap gap-2">
                          {subBill.participants.map((participant) => (
                            <span
                              key={participant.id}
                              className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                            >
                              {participant.name}: ¥{participant.amountToPay.toFixed(2)}
                            </span>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default ComboResultTable;