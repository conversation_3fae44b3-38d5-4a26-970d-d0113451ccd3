import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { DateRangePicker } from '../ui/date-range-picker';
import { Plus, Minus } from 'lucide-react';
import { toast } from 'sonner';
import dayjs from 'dayjs';
import type { Bill } from '../../types';
import { v4 as uuidv4 } from 'uuid';
import { useBill } from '../../contexts/BillContext';

interface BillFormProps {
  initialValues?: Bill;
  onSuccess?: (bill: Bill) => void;
}

interface FormValues {
  name: string;
  totalAmount: number;
  dateRange: { from: Date | undefined; to: Date | undefined };
  participants: { name: string }[];
}

const BillForm: React.FC<BillFormProps> = ({ initialValues, onSuccess }) => {
  const { createBill, updateBill, bills } = useBill();
  const [formData, setFormData] = useState<FormValues>({
    name: '',
    totalAmount: 0,
    dateRange: { from: undefined, to: undefined },
    participants: [{ name: '参与者 1' }, { name: '参与者 2' }]
  });
  const [participantsCount, setParticipantsCount] = useState(2);
  
  // 获取上一次创建的账单信息
  const getLastBill = (): Bill | null => {
    if (bills.length === 0) return null;
    
    // 按创建时间排序，获取最新的账单
    const sortedBills = [...bills].sort((a, b) => 
      new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
    );
    
    return sortedBills[0];
  };
  
  // 设置表单初始值
  useEffect(() => {
    if (initialValues) {
      // 编辑模式：使用传入的初始值
      // 计算结束日期
      const startDate = new Date(initialValues.startDate);
      const endDate = new Date(startDate);
      endDate.setDate(startDate.getDate() + initialValues.days - 1);

      setFormData({
        name: initialValues.name,
        totalAmount: initialValues.totalAmount,
        dateRange: { from: startDate, to: endDate },
        participants: initialValues.participants.map(p => ({ name: p.name }))
      });

      setParticipantsCount(initialValues.participants.length);
    } else {
      // 创建模式：尝试使用上一次账单的日期和参与者作为默认值
      const lastBill = getLastBill();

      if (lastBill) {
        // 使用上一次的日期，但调整为从今天开始
        const today = new Date();
        const lastDays = lastBill.days;
        const endDate = new Date(today);
        endDate.setDate(today.getDate() + lastDays - 1);

        // 使用上一次的参与者列表
        const lastParticipants = lastBill.participants.map(p => ({ name: p.name }));

        setFormData(prev => ({
          ...prev,
          dateRange: { from: today, to: endDate },
          participants: lastParticipants
        }));

        setParticipantsCount(lastParticipants.length);
      }
    }
  }, [initialValues, bills]);
  
  const handleFinish = (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.dateRange.from || !formData.dateRange.to) {
      toast.error('请选择有效的日期范围');
      return;
    }

    // 计算日期范围内的天数
    const startDate = formData.dateRange.from;
    const endDate = formData.dateRange.to;
    const days = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24)) + 1;

    if (days < 1) {
      toast.error('日期范围至少为1天');
      return;
    }

    try {
      let result: Bill;

      if (initialValues) {
        // 更新已有账单
        const updatedData: Bill = {
          ...initialValues,
          name: formData.name,
          totalAmount: formData.totalAmount,
          days,
          startDate: startDate.toISOString(),
          // 处理参与者
          participants: formData.participants.map((p, index) => {
            if (index < initialValues.participants.length) {
              // 已有参与者，保留ID和参与状态
              const existingParticipant = initialValues.participants[index];
              return {
                ...existingParticipant,
                name: p.name,
                // 如果天数变化了，需要调整参与状态数组
                participationDays: days > existingParticipant.participationDays.length
                  ? [...existingParticipant.participationDays, ...Array(days - existingParticipant.participationDays.length).fill(true)]
                  : existingParticipant.participationDays.slice(0, days),
              };
            } else {
              // 新添加的参与者
              return {
                id: uuidv4(),
                name: p.name,
                participationDays: Array(days).fill(true),
                amountToPay: 0
              };
            }
          })
        };

        result = updateBill(updatedData);
        toast.success('账单已更新');
      } else {
        // 创建新账单
        const billData = {
          name: formData.name,
          totalAmount: formData.totalAmount,
          days,
          startDate: startDate.toISOString(),
          participants: formData.participants.map((p) => ({
            id: uuidv4(),
            name: p.name,
            participationDays: Array(days).fill(true),
            amountToPay: 0
          }))
        };

        result = createBill(billData);
        toast.success('账单已创建');
      }

      // 调用成功回调
      if (onSuccess) {
        onSuccess(result);
      }
    } catch (error) {
      console.error('保存账单失败:', error);
      toast.error('保存账单失败，请重试');
    }
  };

  const addParticipant = () => {
    setFormData(prev => ({
      ...prev,
      participants: [...prev.participants, { name: `参与者 ${participantsCount + 1}` }]
    }));
    setParticipantsCount(prev => prev + 1);
  };

  const removeParticipant = (index: number) => {
    setFormData(prev => ({
      ...prev,
      participants: prev.participants.filter((_, i) => i !== index)
    }));
    setParticipantsCount(prev => prev - 1);
  };

  const updateParticipantName = (index: number, name: string) => {
    setFormData(prev => ({
      ...prev,
      participants: prev.participants.map((p, i) => i === index ? { ...p, name } : p)
    }));
  };



  return (
    <Card>
      <CardHeader>
        <CardTitle>{initialValues ? '编辑账单' : '创建账单'}</CardTitle>
        <p className="text-sm text-muted-foreground">设置账单基本信息和参与者</p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleFinish} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="name">账单名称</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="例如：旅行租车费用"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="totalAmount">账单总金额</Label>
            <div className="relative">
              <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">¥</span>
              <Input
                id="totalAmount"
                type="number"
                value={formData.totalAmount}
                onChange={(e) => setFormData(prev => ({ ...prev, totalAmount: parseFloat(e.target.value) || 0 }))}
                placeholder="输入总金额"
                className="pl-8"
                step="0.01"
                min="0"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label>时间范围</Label>
            <DateRangePicker
              value={formData.dateRange}
              onChange={(range) => setFormData(prev => ({ ...prev, dateRange: range }))}
              placeholder={['开始日期', '结束日期']}
            />
          </div>

          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium">参与者</h4>
              <p className="text-sm text-muted-foreground">添加所有可能参与的人员，后续可设置每天的具体参与情况</p>
            </div>

            <div className="space-y-3">
              {formData.participants.map((participant, index) => (
                <div key={index} className="flex items-center gap-2">
                  <Input
                    value={participant.name}
                    onChange={(e) => updateParticipantName(index, e.target.value)}
                    placeholder="参与者名称"
                    required
                  />
                  {formData.participants.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeParticipant(index)}
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}

              <Button
                type="button"
                variant="outline"
                onClick={addParticipant}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                添加参与者
              </Button>
            </div>
          </div>

          <Button type="submit" className="w-full" size="lg">
            {initialValues ? '保存修改' : '下一步：设置参与度'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default BillForm;