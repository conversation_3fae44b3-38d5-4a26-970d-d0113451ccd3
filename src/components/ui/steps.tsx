import React from 'react';
import { cn } from '../../lib/utils';

interface StepItem {
  title: string;
  disabled?: boolean;
}

interface StepsProps {
  current: number;
  items: StepItem[];
  onChange?: (current: number) => void;
  className?: string;
}

export const Steps: React.FC<StepsProps> = ({ current, items, onChange, className }) => {
  return (
    <div className={cn("flex items-center justify-between w-full", className)}>
      {items.map((item, index) => (
        <React.Fragment key={index}>
          <div 
            className={cn(
              "flex items-center cursor-pointer",
              item.disabled && "cursor-not-allowed opacity-50"
            )}
            onClick={() => !item.disabled && onChange?.(index)}
          >
            <div 
              className={cn(
                "flex items-center justify-center w-8 h-8 rounded-full border-2 text-sm font-medium",
                index === current 
                  ? "bg-primary text-primary-foreground border-primary" 
                  : index < current 
                    ? "bg-primary text-primary-foreground border-primary"
                    : "bg-background text-muted-foreground border-muted-foreground"
              )}
            >
              {index + 1}
            </div>
            <span 
              className={cn(
                "ml-2 text-sm font-medium",
                index === current ? "text-primary" : "text-muted-foreground"
              )}
            >
              {item.title}
            </span>
          </div>
          {index < items.length - 1 && (
            <div 
              className={cn(
                "flex-1 h-0.5 mx-4",
                index < current ? "bg-primary" : "bg-muted"
              )}
            />
          )}
        </React.Fragment>
      ))}
    </div>
  );
};
