import React, { useState, useEffect } from 'react';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Plus, Minus, PlusCircle } from 'lucide-react';
import { toast } from 'sonner';
import { v4 as uuidv4 } from 'uuid';
import type { Bill, ComboBillFormData, SubBill } from '../../types';
import { useBill } from '../../contexts/BillContext';

interface ComboBillFormProps {
  initialValues?: Bill;
  onSuccess?: (bill: Bill) => void;
}

const ComboBillForm: React.FC<ComboBillFormProps> = ({ initialValues, onSuccess }) => {
  const { createBill, updateBill } = useBill();
  const [formData, setFormData] = useState<ComboBillFormData>({
    name: '未命名组合账单',
    participants: [
      { id: uuidv4(), name: '参与者 1' },
      { id: uuidv4(), name: '参与者 2' }
    ],
    subBills: [{ name: '子订单 1', amount: 0, participants: [] }]
  });
  
  // 设置表单初始值
  useEffect(() => {
    if (initialValues && initialValues.isCombo && initialValues.subBills) {
      setFormData({
        name: initialValues.name,
        participants: initialValues.participants.map(p => ({ id: p.id, name: p.name })),
        subBills: initialValues.subBills.map(sb => ({
          name: sb.name,
          amount: sb.amount,
          participants: sb.participants.map(p => p.id)
        }))
      });
    }
  }, [initialValues]);
  
  const handleFinish = (e: React.FormEvent) => {
    e.preventDefault();

    try {
      let result: Bill;

      // 构建子订单数据
      const subBills: SubBill[] = formData.subBills.map(sb => {
        // 查找每个子订单的参与者完整信息
        const subBillParticipants = sb.participants.map(participantId => {
          const participant = formData.participants.find(p => p.id === participantId);
          return {
            id: participantId,
            name: participant?.name || '未知参与者',
            amountToPay: 0 // 初始值，将在计算时更新
          };
        });

        return {
          id: uuidv4(),
          name: sb.name,
          amount: sb.amount,
          participants: subBillParticipants
        };
      });

      // 计算总金额
      const totalAmount = subBills.reduce((sum, sb) => sum + sb.amount, 0);
      
      if (initialValues && initialValues.id) {
        // 更新已有账单
        const updatedData: Bill = {
          ...initialValues,
          name: formData.name,
          totalAmount,
          isCombo: true,
          subBills,
          // 更新参与者列表，但保留原有ID
          participants: formData.participants.map(p => {
            const existingParticipant = initialValues.participants.find(ep => ep.id === p.id);
            if (existingParticipant) {
              return {
                ...existingParticipant,
                name: p.name,
                amountToPay: 0 // 将在计算时更新
              };
            } else {
              return {
                id: p.id,
                name: p.name,
                participationDays: initialValues.days ? Array(initialValues.days).fill(true) : [],
                amountToPay: 0
              };
            }
          })
        };

        result = updateBill(updatedData);
        toast.success('组合账单已更新');
      } else {
        // 创建新账单
        const now = new Date().toISOString();
        const billData = {
          name: formData.name,
          totalAmount,
          days: 1, // 组合订单不需要天数，但为了兼容性设为1
          startDate: now,
          isCombo: true,
          subBills,
          participants: formData.participants.map(p => ({
            id: p.id,
            name: p.name,
            participationDays: [true], // 组合订单不使用参与天数
            amountToPay: 0 // 将在计算时更新
          }))
        };

        result = createBill(billData);
        toast.success('组合账单已创建');
      }

      // 调用成功回调
      if (onSuccess) {
        onSuccess(result);
      }
    } catch (error) {
      console.error('保存组合账单失败:', error);
      toast.error('保存组合账单失败，请重试');
    }
  };

  // 添加参与者
  const addParticipant = () => {
    const newParticipant = { id: uuidv4(), name: `参与者 ${formData.participants.length + 1}` };
    setFormData(prev => ({
      ...prev,
      participants: [...prev.participants, newParticipant]
    }));
  };

  // 移除参与者
  const removeParticipant = (id: string) => {
    // 检查是否有子订单使用了该参与者
    const isUsed = formData.subBills.some(sb =>
      sb.participants && sb.participants.includes(id)
    );

    if (isUsed) {
      toast.error('该参与者已在子订单中使用，无法删除');
      return;
    }

    setFormData(prev => ({
      ...prev,
      participants: prev.participants.filter(p => p.id !== id)
    }));
  };

  // 修改参与者名称
  const updateParticipantName = (id: string, newName: string) => {
    setFormData(prev => ({
      ...prev,
      participants: prev.participants.map(p =>
        p.id === id ? { ...p, name: newName } : p
      )
    }));
  };

  // 添加子订单
  const addSubBill = () => {
    setFormData(prev => ({
      ...prev,
      subBills: [...prev.subBills, { name: `子订单 ${prev.subBills.length + 1}`, amount: 0, participants: [] }]
    }));
  };

  // 移除子订单
  const removeSubBill = (index: number) => {
    setFormData(prev => ({
      ...prev,
      subBills: prev.subBills.filter((_, i) => i !== index)
    }));
  };

  // 更新子订单
  const updateSubBill = (index: number, field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      subBills: prev.subBills.map((sb, i) =>
        i === index ? { ...sb, [field]: value } : sb
      )
    }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{initialValues ? '编辑组合账单' : '创建组合账单'}</CardTitle>
        <p className="text-sm text-muted-foreground">设置组合账单信息和子订单</p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleFinish} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="name">账单名称</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
              placeholder="例如：聚餐组合账单"
              required
            />
          </div>

          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium">参与者列表</h4>
              <p className="text-sm text-muted-foreground">添加所有可能参与的人员</p>
            </div>

            <div className="space-y-3">
              {formData.participants.map((participant, index) => (
                <div key={participant.id} className="flex items-center gap-2">
                  <Input
                    value={participant.name}
                    onChange={(e) => updateParticipantName(participant.id, e.target.value)}
                    placeholder="参与者名称"
                    required
                  />
                  {formData.participants.length > 1 && (
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => removeParticipant(participant.id)}
                    >
                      <Minus className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              ))}

              <Button
                type="button"
                variant="outline"
                onClick={addParticipant}
                className="w-full"
              >
                <Plus className="h-4 w-4 mr-2" />
                添加参与者
              </Button>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <h4 className="text-sm font-medium">子订单列表</h4>
              <p className="text-sm text-muted-foreground">添加组成组合账单的各个子订单</p>
            </div>

            <div className="space-y-4">
              {formData.subBills.map((subBill, index) => (
                <Card key={index}>
                  <CardHeader className="pb-3">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-base">子订单 {index + 1}</CardTitle>
                      {formData.subBills.length > 1 && (
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          onClick={() => removeSubBill(index)}
                        >
                          <Minus className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-2">
                      <Label htmlFor={`subBill-name-${index}`}>子订单名称</Label>
                      <Input
                        id={`subBill-name-${index}`}
                        value={subBill.name}
                        onChange={(e) => updateSubBill(index, 'name', e.target.value)}
                        placeholder="例如：正餐、酒水、水果等"
                        required
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor={`subBill-amount-${index}`}>子订单金额</Label>
                      <div className="relative">
                        <span className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">¥</span>
                        <Input
                          id={`subBill-amount-${index}`}
                          type="number"
                          value={subBill.amount}
                          onChange={(e) => updateSubBill(index, 'amount', parseFloat(e.target.value) || 0)}
                          placeholder="输入金额"
                          className="pl-8"
                          step="0.01"
                          min="0"
                          required
                        />
                      </div>
                    </div>

                    <div className="space-y-2">
                      <Label>参与者</Label>
                      <div className="space-y-2">
                        {formData.participants.map((participant) => (
                          <label key={participant.id} className="flex items-center space-x-2">
                            <input
                              type="checkbox"
                              checked={subBill.participants.includes(participant.id)}
                              onChange={(e) => {
                                const newParticipants = e.target.checked
                                  ? [...subBill.participants, participant.id]
                                  : subBill.participants.filter(id => id !== participant.id);
                                updateSubBill(index, 'participants', newParticipants);
                              }}
                              className="rounded border-gray-300"
                            />
                            <span className="text-sm">{participant.name}</span>
                          </label>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}

              <Button
                type="button"
                variant="outline"
                onClick={addSubBill}
                className="w-full"
              >
                <PlusCircle className="h-4 w-4 mr-2" />
                添加子订单
              </Button>
            </div>
          </div>

          <Button type="submit" className="w-full" size="lg">
            {initialValues ? '保存修改' : '创建组合账单'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default ComboBillForm; 