import React from 'react';
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Cell } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import type { Bill } from '../../types';

interface AmountBarChartProps {
  bill: Bill;
  hideTitle?: boolean;
}

const AmountBarChart: React.FC<AmountBarChartProps> = ({ bill, hideTitle }) => {
  // 检查是否是普通账单
  if (bill.isCombo) {
    return <p className="text-yellow-600">此组件仅适用于普通账单</p>;
  }

  // 构建图表数据
  const chartData = bill.participants.map(participant => {
    const daysCount = participant.participationDays.filter(day => day).length;
    const daysPercentage = ((daysCount / bill.days) * 100).toFixed(1);
    
    // 计算参与度比例，用于颜色渐变
    const participationRatio = daysCount / bill.days;
    // 根据参与度生成颜色，参与度越高颜色越深
    const barColor = participationRatio >= 0.8 ? '#1890ff' : 
                    participationRatio >= 0.5 ? '#40a9ff' : 
                    participationRatio >= 0.3 ? '#69c0ff' : '#91d5ff';
                    
    return {
      name: participant.name,
      amount: participant.amountToPay,
      daysCount,
      daysPercentage,
      fill: barColor
    };
  }).sort((a, b) => b.amount - a.amount);  // 按金额降序排列

  // 自定义工具提示，使用any类型避免类型错误
  const CustomTooltip = ({ active, payload }: { active?: boolean; payload?: any[] }) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <div style={{ 
          backgroundColor: 'rgba(255, 255, 255, 0.98)', 
          padding: '8px 12px', 
          border: '1px solid #d9d9d9',
          boxShadow: '0 2px 8px rgba(0, 0, 0, 0.15)',
          borderRadius: '4px',
          fontSize: '13px'
        }}>
          <p style={{ margin: '0', fontWeight: 'bold', borderBottom: '1px solid #f0f0f0', paddingBottom: '4px' }}>{data.name}</p>
          <p style={{ margin: '6px 0 0', color: '#262626' }}>应付金额: <span style={{ fontWeight: '500', color: '#1890ff' }}>¥{Number(data.amount).toFixed(2)}</span></p>
          <p style={{ margin: '4px 0 0', color: '#262626' }}>参与天数: <span style={{ fontWeight: '500' }}>{data.daysCount}天 ({data.daysPercentage}%)</span></p>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="mb-6">
      {!hideTitle && (
        <CardHeader>
          <CardTitle className="text-lg">分摊金额分布图</CardTitle>
        </CardHeader>
      )}
      <CardContent>
        <div style={{ width: '100%', height: hideTitle ? 400 : 350, overflow: 'hidden' }}>
          <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={chartData}
            margin={{ top: 20, right: 25, left: 0, bottom: 20 }}
            barSize={hideTitle ? 28 : 35}
            barGap={2}
          >
            <CartesianGrid strokeDasharray="3 3" strokeOpacity={0.6} />
            <XAxis 
              dataKey="name" 
              angle={-45} 
              textAnchor="end"
              height={50}
              interval={0}
              tick={{ fontSize: 12 }}
              tickLine={{ stroke: '#d9d9d9' }}
            />
            <YAxis 
              tickFormatter={(value) => `¥${value}`}
              label={hideTitle ? undefined : { value: '金额 (¥)', angle: -90, position: 'insideLeft' }}
              width={40}
              tick={{ fontSize: 11 }}
              tickLine={{ stroke: '#d9d9d9' }}
            />
            <Tooltip content={<CustomTooltip />} />
            <Bar 
              dataKey="amount" 
              name="应付金额" 
              fill="#1890ff" 
              radius={[4, 4, 0, 0]}
              animationDuration={800}
            >
              {chartData.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.fill} />
              ))}
            </Bar>
          </BarChart>
          </ResponsiveContainer>
        </div>

        <div className="mt-4">
          <p className="text-sm text-muted-foreground">
            此图表展示了每个参与者需要分摊的金额大小，按金额从高到低排序。柱高表示金额大小。
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default AmountBarChart; 