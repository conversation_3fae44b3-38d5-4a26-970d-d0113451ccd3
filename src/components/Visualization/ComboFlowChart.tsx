import React from 'react';
import { San<PERSON>, <PERSON>ltip, ResponsiveContainer } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import type { Bill } from '../../types';

interface ComboFlowChartProps {
  bill: Bill;
}

interface SankeyNode {
  name: string;
  value?: number;
  fill?: string;
  type: 'participant' | 'subbill';
}

interface SankeyLink {
  source: number;
  target: number;
  value: number;
  sourceName: string;
  targetName: string;
}

const ComboFlowChart: React.FC<ComboFlowChartProps> = ({ bill }) => {
  // 检查是否是组合账单
  if (!bill.isCombo || !bill.subBills || bill.subBills.length === 0) {
    return (
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="text-lg">资金流向图</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col items-center justify-center h-32 text-muted-foreground">
            <p className="text-sm">此组件仅适用于组合账单</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  // 颜色配置
  const participantColor = '#1890ff';
  const subbillColor = '#52c41a';

  // 构建桑基图数据
  // 节点包括所有参与者和子订单
  const nodes: SankeyNode[] = [];
  const links: SankeyLink[] = [];

  // 添加参与者节点
  bill.participants.forEach(participant => {
    nodes.push({
      name: participant.name,
      value: participant.amountToPay,
      fill: participantColor,
      type: 'participant'
    });
  });

  // 添加子订单节点
  bill.subBills.forEach(subBill => {
    nodes.push({
      name: subBill.name,
      value: subBill.amount,
      fill: subbillColor,
      type: 'subbill'
    });
  });

  // 添加链接（从参与者到子订单的资金流向）
  bill.subBills.forEach((subBill, subBillIndex) => {
    const targetIndex = bill.participants.length + subBillIndex;
    
    subBill.participants.forEach(subBillParticipant => {
      // 找到参与者在原列表中的索引
      const sourceIndex = bill.participants.findIndex(p => p.id === subBillParticipant.id);
      if (sourceIndex !== -1) {
        links.push({
          source: sourceIndex,
          target: targetIndex,
          value: subBillParticipant.amountToPay,
          sourceName: subBillParticipant.name,
          targetName: subBill.name
        });
      }
    });
  });

  // 图表配置
  const chartData = { nodes, links };

  // 自定义工具提示，避免类型错误，使用 any
  const CustomTooltip = ({ payload }: { payload?: any[] }) => {
    if (!payload || !payload.length) return null;

    const item = payload[0];
    if (!item) return null;

    const data = item.payload;

    if (item.name === 'link') {
      return (
        <div style={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', padding: '10px', border: '1px solid #ccc', boxShadow: '0 2px 8px rgba(0,0,0,0.15)' }}>
          <p style={{ fontWeight: 'bold', margin: 0 }}>{`${data.sourceName} → ${data.targetName}`}</p>
          <p style={{ margin: '5px 0 0' }}>{`支付金额: ¥${Number(data.value).toFixed(2)}`}</p>
        </div>
      );
    }

    return (
      <div style={{ backgroundColor: 'rgba(255, 255, 255, 0.9)', padding: '10px', border: '1px solid #ccc', boxShadow: '0 2px 8px rgba(0,0,0,0.15)' }}>
        <p style={{ fontWeight: 'bold', margin: 0 }}>{data.name}</p>
        <p style={{ margin: '5px 0 0' }}>{`类型: ${data.type === 'participant' ? '参与者' : '子订单'}`}</p>
        {data.value !== undefined && <p style={{ margin: '5px 0 0' }}>{`金额: ¥${Number(data.value).toFixed(2)}`}</p>}
      </div>
    );
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="text-lg">资金流向图</CardTitle>
      </CardHeader>
      <CardContent>
        <div style={{ width: '100%', height: 400 }}>
          <ResponsiveContainer width="100%" height="100%">
          <Sankey
            data={chartData}
            nodePadding={50}
            nodeWidth={10}
            linkCurvature={0.5}
            margin={{ top: 30, right: 100, bottom: 30, left: 100 }}
          >
            <Tooltip content={<CustomTooltip />} />
          </Sankey>
        </ResponsiveContainer>
      </div>
      
        {/* 图例和节点标签 */}
        <div className="mt-4 flex justify-between">
          <div>
            <h5 className="text-sm font-medium mb-2">参与者</h5>
            {nodes.filter(node => node.type === 'participant').map((node, index) => (
              <div key={`p-${index}`} className="flex items-center my-1">
                <span
                  className="inline-block w-3 h-3 mr-2"
                  style={{ backgroundColor: participantColor }}
                ></span>
                <span className="text-sm">{node.name}: ¥{Number(node.value).toFixed(2)}</span>
              </div>
            ))}
          </div>

          <div>
            <h5 className="text-sm font-medium mb-2">子订单</h5>
            {nodes.filter(node => node.type === 'subbill').map((node, index) => (
              <div key={`s-${index}`} className="flex items-center my-1">
                <span
                  className="inline-block w-3 h-3 mr-2"
                  style={{ backgroundColor: subbillColor }}
                ></span>
                <span className="text-sm">{node.name}: ¥{Number(node.value).toFixed(2)}</span>
              </div>
            ))}
          </div>
        </div>

        <div className="mt-4">
          <p className="text-sm text-muted-foreground">
            此流向图展示了组合账单中参与者与子订单之间的资金流向。左侧为参与者，右侧为子订单，连线的粗细表示金额的大小。
            悬停在节点或连线上可查看详细信息。
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default ComboFlowChart; 