import React from 'react';
import { ResponsiveContainer, Rectangle, Surface } from 'recharts';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import type { Bill } from '../../types';
import dayjs from 'dayjs';

interface ParticipationHeatmapProps {
  bill: Bill;
  hideTitle?: boolean;
}

const ParticipationHeatmap: React.FC<ParticipationHeatmapProps> = ({ bill, hideTitle }) => {
  // 检查是否是普通账单
  if (bill.isCombo) {
    return <p className="text-yellow-600">此组件仅适用于普通账单</p>;
  }

  // 设置热力图参数
  const maxParticipants = 10; // 最多显示的参与者数量，超过时调整单元格大小
  const isCompact = bill.participants.length > maxParticipants || bill.days > 14;
  
  // 根据参与者数量和天数动态调整单元格大小
  const cellSize = isCompact ? 28 : 36;  // 垂直布局可以使用更大的单元格
  const cellMargin = 3;
  const legendHeight = 30;
  const headerHeight = 50;
  const labelWidth = 80;
  
  // 计算总宽度，确保右侧有足够空间显示参与天数
  const width = labelWidth + (cellSize + cellMargin) * bill.days + 80;
  
  // 动态计算高度，但设置最小值
  const minHeight = 300;
  const calculatedHeight = headerHeight + (cellSize + cellMargin) * bill.participants.length + legendHeight;
  const height = Math.max(minHeight, calculatedHeight);

  // 日期标签
  const dateLabels = Array.from({ length: bill.days }, (_, index) => {
    const date = dayjs(bill.startDate).add(index, 'day');
    return {
      day: index + 1,
      date: date.format('MM-DD'),
      weekday: ['日', '一', '二', '三', '四', '五', '六'][date.day()]
    };
  });

  // 生成热力图颜色
  const getParticipationColor = (isParticipating: boolean): string => {
    return isParticipating ? '#52c41a' : '#ff4d4f';
  };
  
  // 图例样式
  const legendStyle = {
    participatingFill: '#52c41a',
    nonParticipatingFill: '#ff4d4f',
    participatingStroke: '#3f9313',
    nonParticipatingStroke: '#cf1322'
  };

  // 计算每个参与者的总参与度
  const participantStats = bill.participants.map(participant => {
    const daysCount = participant.participationDays.filter(day => day).length;
    const percentage = ((daysCount / bill.days) * 100).toFixed(1);
    return {
      ...participant,
      daysCount,
      percentage
    };
  });

  // 渲染热力图
  return (
    <Card className="mb-6">
      {!hideTitle && (
        <CardHeader>
          <CardTitle className="text-lg">参与度热力图</CardTitle>
        </CardHeader>
      )}
      <CardContent>
      <div style={{ position: 'relative' }}>
        <ResponsiveContainer width="100%" height={height}>
          <Surface width={width} height={height}>
            {/* 日期标签行 */}
            {dateLabels.map((label, dayIndex) => (
              <React.Fragment key={`day-${dayIndex}`}>
                <text
                  x={labelWidth + dayIndex * (cellSize + cellMargin) + cellSize / 2}
                  y={15}
                  textAnchor="middle"
                  dominantBaseline="middle"
                  fontSize={10}
                >
                  第{label.day}天
                </text>
                <text
                  x={labelWidth + dayIndex * (cellSize + cellMargin) + cellSize / 2}
                  y={30}
                  textAnchor="middle"
                  dominantBaseline="middle"
                  fontSize={10}
                  fill="#888"
                >
                  {label.date}
                </text>
                <text
                  x={labelWidth + dayIndex * (cellSize + cellMargin) + cellSize / 2}
                  y={42}
                  textAnchor="middle"
                  dominantBaseline="middle"
                  fontSize={10}
                  fill="#888"
                >
                  周{label.weekday}
                </text>
              </React.Fragment>
            ))}

            {/* 参与者标签和热力图单元格 */}
            {bill.participants.map((participant, participantIndex) => {
              return (
                <React.Fragment key={participant.id}>
                  {/* 参与者名称 */}
                  <text
                    x={labelWidth - 5}
                    y={headerHeight + participantIndex * (cellSize + cellMargin) + cellSize / 2}
                    textAnchor="end"
                    dominantBaseline="middle"
                    fontSize={12}
                  >
                    {participant.name}
                  </text>
                  
                  {/* 参与度热力格子 */}
                  {participant.participationDays.map((isParticipating, dayIndex) => {
                    return (
                      <Rectangle
                        key={`${participant.id}-day-${dayIndex}`}
                        x={labelWidth + dayIndex * (cellSize + cellMargin)}
                        y={headerHeight + participantIndex * (cellSize + cellMargin)}
                        width={cellSize}
                        height={cellSize}
                        radius={isCompact ? 2 : 4}
                        fill={getParticipationColor(isParticipating)}
                        stroke={isParticipating ? '#3f9313' : '#cf1322'}
                        strokeWidth={0.5}
                        opacity={isParticipating ? 0.9 : 0.85}
                      />
                    );
                  })}
                  
                  {/* 总参与度统计 */}
                  <text
                    x={labelWidth + bill.days * (cellSize + cellMargin) + 5}
                    y={headerHeight + participantIndex * (cellSize + cellMargin) + cellSize / 2}
                    dominantBaseline="middle"
                    fontSize={10}
                  >
                    {participantStats[participantIndex].daysCount}天
                    ({participantStats[participantIndex].percentage}%)
                  </text>
                </React.Fragment>
              );
            })}

            {/* 图例 */}
            <Rectangle
              x={labelWidth}
              y={height - legendHeight + 5}
              width={12}
              height={12}
              radius={2}
              fill={legendStyle.participatingFill}
              stroke={legendStyle.participatingStroke}
              strokeWidth={0.5}
            />
            <text
              x={labelWidth + 16}
              y={height - legendHeight + 11}
              dominantBaseline="middle"
              fontSize={10}
              fontWeight="500"
            >
              参与
            </text>
            
            <Rectangle
              x={labelWidth + 60}
              y={height - legendHeight + 5}
              width={12}
              height={12}
              radius={2}
              fill={legendStyle.nonParticipatingFill}
              stroke={legendStyle.nonParticipatingStroke}
              strokeWidth={0.5}
            />
            <text
              x={labelWidth + 76}
              y={height - legendHeight + 11}
              dominantBaseline="middle"
              fontSize={10}
              fontWeight="500"
            >
              未参与
            </text>
          </Surface>
        </ResponsiveContainer>
        </div>

        <div className="mt-4">
          <p className="text-sm text-muted-foreground">
            此热力图直观展示了每位参与者在每一天的参与情况，绿色表示参与，红色表示未参与。
          </p>
        </div>
      </CardContent>
    </Card>
  );
};

export default ParticipationHeatmap; 