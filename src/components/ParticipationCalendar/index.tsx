import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Check, X } from 'lucide-react';
import type { Bill, Participant } from '../../types';
import dayjs from 'dayjs';
import { useBill } from '../../contexts/BillContext';
import { calculateBill } from '../../utils/calculator';

interface ParticipationCalendarProps {
  bill: Bill;
  onSave?: () => void;
}

const ParticipationCalendar: React.FC<ParticipationCalendarProps> = ({ bill, onSave }) => {
  const { updateBill } = useBill();
  const [participants, setParticipants] = useState<Participant[]>(bill.participants);

  // 当账单信息变更时更新本地状态
  useEffect(() => {
    setParticipants(bill.participants);
  }, [bill]);

  // 切换参与状态
  const toggleParticipation = (participantIndex: number, dayIndex: number) => {
    const newParticipants = [...participants];
    newParticipants[participantIndex].participationDays[dayIndex] =
      !newParticipants[participantIndex].participationDays[dayIndex];
    setParticipants(newParticipants);
  };

  // 保存参与度设置
  const handleSave = () => {
    const updatedBill = calculateBill({
      ...bill,
      participants
    });

    updateBill(updatedBill);
    onSave?.();
  };

  // 生成日期数组
  const dates = Array.from({ length: bill.days }, (_, index) => {
    const date = dayjs(bill.startDate).add(index, 'day');
    return {
      day: index + 1,
      date: date.format('MM-DD'),
      weekday: date.format('ddd')
    };
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>设置参与度</CardTitle>
        <p className="text-sm text-muted-foreground">
          设置每位参与者在每一天的参与情况，系统将根据参与度计算分摊金额
        </p>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <table className="w-full border-collapse">
            <thead>
              <tr>
                <th className="border p-2 text-left">参与者</th>
                {dates.map((date) => (
                  <th key={date.day} className="border p-2 text-center min-w-[80px]">
                    <div className="text-xs">
                      <div>第{date.day}天</div>
                      <div className="text-muted-foreground">{date.date}</div>
                      <div className="text-muted-foreground">{date.weekday}</div>
                    </div>
                  </th>
                ))}
                <th className="border p-2 text-center">参与天数</th>
              </tr>
            </thead>
            <tbody>
              {participants.map((participant, participantIndex) => (
                <tr key={participant.id}>
                  <td className="border p-2 font-medium">{participant.name}</td>
                  {participant.participationDays.map((isParticipating, dayIndex) => (
                    <td key={dayIndex} className="border p-2 text-center">
                      <input
                        type="checkbox"
                        checked={isParticipating}
                        onChange={() => toggleParticipation(participantIndex, dayIndex)}
                        className="w-4 h-4"
                      />
                    </td>
                  ))}
                  <td className="border p-2 text-center">
                    {participant.participationDays.filter(Boolean).length} / {bill.days}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>

        <div className="mt-6 flex justify-end">
          <Button onClick={handleSave} size="lg">
            保存并计算分摊
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};

export default ParticipationCalendar; 