import React, { createContext, useContext, useState, useEffect } from 'react';
import type { ReactNode } from 'react';
import type { Bill } from '../types';
import { getBills, saveBill, deleteBill } from '../utils/storage';
import { calculateBill } from '../utils/calculator';
import { v4 as uuidv4 } from 'uuid';

// 上下文类型定义
interface BillContextType {
  bills: Bill[];
  currentBill: Bill | null;
  loading: boolean;
  createBill: (bill: Omit<Bill, 'id' | 'createdAt' | 'updatedAt'>) => Bill;
  updateBill: (bill: Bill) => Bill;
  removeBill: (id: string) => void;
  getBill: (id: string) => Bill | undefined;
  setCurrentBill: (bill: Bill | null) => void;
}

// 创建上下文
const BillContext = createContext<BillContextType | undefined>(undefined);

// 上下文Provider组件
interface BillProviderProps {
  children: ReactNode;
}

export const BillProvider: React.FC<BillProviderProps> = ({ children }) => {
  const [bills, setBills] = useState<Bill[]>([]);
  const [currentBill, setCurrentBill] = useState<Bill | null>(null);
  const [loading, setLoading] = useState(true);

  // 初始化时从本地存储加载账单数据
  useEffect(() => {
    const loadedBills = getBills();
    setBills(loadedBills);
    setLoading(false);
  }, []);

  // 创建新账单
  const createBill = (billData: Omit<Bill, 'id' | 'createdAt' | 'updatedAt'>): Bill => {
    const now = new Date().toISOString();
    const newBill: Bill = {
      ...billData,
      id: uuidv4(),
      createdAt: now,
      updatedAt: now,
    };
    
    // 计算每个参与者应付的金额
    const calculatedBill = calculateBill(newBill);
    
    // 保存到本地存储
    saveBill(calculatedBill);
    
    // 更新状态
    setBills(prevBills => [...prevBills, calculatedBill]);
    
    return calculatedBill;
  };

  // 更新账单
  const updateBill = (bill: Bill): Bill => {
    const updatedBill = {
      ...bill,
      updatedAt: new Date().toISOString(),
    };
    
    // 重新计算金额
    const calculatedBill = calculateBill(updatedBill);
    
    // 保存到本地存储
    saveBill(calculatedBill);
    
    // 更新状态
    setBills(prevBills => 
      prevBills.map(b => b.id === calculatedBill.id ? calculatedBill : b)
    );
    
    // 如果当前正在查看的账单被更新，也更新currentBill
    if (currentBill && currentBill.id === calculatedBill.id) {
      setCurrentBill(calculatedBill);
    }

    return calculatedBill;
  };

  // 删除账单
  const removeBill = (id: string) => {
    // 从本地存储删除
    deleteBill(id);
    
    // 更新状态
    setBills(prevBills => prevBills.filter(bill => bill.id !== id));
    
    // 如果当前正在查看的账单被删除，清除currentBill
    if (currentBill && currentBill.id === id) {
      setCurrentBill(null);
    }
  };

  // 获取特定账单
  const getBill = (id: string) => {
    return bills.find(bill => bill.id === id);
  };

  const value = {
    bills,
    currentBill,
    loading,
    createBill,
    updateBill,
    removeBill,
    getBill,
    setCurrentBill,
  };

  return <BillContext.Provider value={value}>{children}</BillContext.Provider>;
};

// 自定义Hook，用于使用账单上下文
export const useBill = (): BillContextType => {
  const context = useContext(BillContext);
  if (context === undefined) {
    throw new Error('useBill must be used within a BillProvider');
  }
  return context;
};